// SPDX-License-Identifier: GPL-2.0+
/*
 * GC9307 SPI LCD driver for U-Boot
 * Based on Linux kernel gc9307.c driver
 * 
 * Copyright (C) 2025 Victel
 * Author: liming
 */

#include <common.h>
#include <dm.h>
#include <spi.h>
#include <video.h>
#include <asm/gpio.h>
#include <linux/delay.h>
#include <malloc.h>
#include <dm/device.h>
#include <dm/uclass.h>
#include <dm/read.h>
#include <dm/device_compat.h>

#define LCD_WIDTH   320
#define LCD_HEIGHT  240

/* GC9307 register commands */
#define GC9307_SWRESET      0x01
#define GC9307_SLPOUT       0x11
#define GC9307_DISPON       0x29
#define GC9307_CASET        0x2A
#define GC9307_RASET        0x2B
#define GC9307_RAMWR        0x2C
#define GC9307_MADCTL       0x36
#define GC9307_COLMOD       0x3A

struct gc9307_priv {
    struct udevice *spi_dev;
    struct gpio_desc reset_gpio;
    struct gpio_desc dc_gpio;
    struct gpio_desc cs_gpio;
    struct gpio_desc led_gpio;
    u32 width;
    u32 height;
    u32 rotate;
};

static int gc9307_spi_write_cmd(struct udevice *dev, u8 cmd)
{
    struct gc9307_priv *priv = dev_get_priv(dev);
    int ret;
    
    /* Set DC low for command */
    ret = dm_gpio_set_value(&priv->dc_gpio, 0);
    if (ret)
        return ret;
        
    return dm_spi_xfer(priv->spi_dev, 8, &cmd, NULL, SPI_XFER_BEGIN | SPI_XFER_END);
}

static int gc9307_spi_write_data(struct udevice *dev, u8 data)
{
    struct gc9307_priv *priv = dev_get_priv(dev);
    int ret;
    
    /* Set DC high for data */
    ret = dm_gpio_set_value(&priv->dc_gpio, 1);
    if (ret)
        return ret;
        
    return dm_spi_xfer(priv->spi_dev, 8, &data, NULL, SPI_XFER_BEGIN | SPI_XFER_END);
}

static int gc9307_spi_write_data_buf(struct udevice *dev, const u8 *buf, size_t len)
{
    struct gc9307_priv *priv = dev_get_priv(dev);
    int ret;
    
    /* Set DC high for data */
    ret = dm_gpio_set_value(&priv->dc_gpio, 1);
    if (ret)
        return ret;
        
    return dm_spi_xfer(priv->spi_dev, len * 8, buf, NULL, SPI_XFER_BEGIN | SPI_XFER_END);
}

static int gc9307_set_address_window(struct udevice *dev, u16 x1, u16 y1, u16 x2, u16 y2)
{
    int ret;
    
    /* Column address set */
    ret = gc9307_spi_write_cmd(dev, GC9307_CASET);
    if (ret) return ret;
    ret = gc9307_spi_write_data(dev, x1 >> 8);
    if (ret) return ret;
    ret = gc9307_spi_write_data(dev, x1 & 0xFF);
    if (ret) return ret;
    ret = gc9307_spi_write_data(dev, x2 >> 8);
    if (ret) return ret;
    ret = gc9307_spi_write_data(dev, x2 & 0xFF);
    if (ret) return ret;
    
    /* Row address set */
    ret = gc9307_spi_write_cmd(dev, GC9307_RASET);
    if (ret) return ret;
    ret = gc9307_spi_write_data(dev, y1 >> 8);
    if (ret) return ret;
    ret = gc9307_spi_write_data(dev, y1 & 0xFF);
    if (ret) return ret;
    ret = gc9307_spi_write_data(dev, y2 >> 8);
    if (ret) return ret;
    ret = gc9307_spi_write_data(dev, y2 & 0xFF);
    if (ret) return ret;
    
    return 0;
}

static int gc9307_clear_screen(struct udevice *dev)
{
    struct gc9307_priv *priv = dev_get_priv(dev);
    u8 *buffer;
    size_t buffer_size = priv->width * priv->height * 2; /* 16-bit color */
    int ret;
    
    buffer = malloc(buffer_size);
    if (!buffer)
        return -ENOMEM;
        
    /* Fill with black (0x0000) */
    memset(buffer, 0x00, buffer_size);
    
    ret = gc9307_set_address_window(dev, 0, 0, priv->width - 1, priv->height - 1);
    if (ret)
        goto cleanup;
        
    ret = gc9307_spi_write_cmd(dev, GC9307_RAMWR);
    if (ret)
        goto cleanup;
        
    ret = gc9307_spi_write_data_buf(dev, buffer, buffer_size);
    
cleanup:
    free(buffer);
    return ret;
}

static int gc9307_init_display(struct udevice *dev)
{
    struct gc9307_priv *priv = dev_get_priv(dev);
    int ret;
    
    /* Hardware reset */
    ret = dm_gpio_set_value(&priv->reset_gpio, 1);
    if (ret) return ret;
    mdelay(50);
    
    ret = dm_gpio_set_value(&priv->reset_gpio, 0);
    if (ret) return ret;
    mdelay(50);
    
    ret = dm_gpio_set_value(&priv->reset_gpio, 1);
    if (ret) return ret;
    mdelay(120);
    
    /* Software reset */
    ret = gc9307_spi_write_cmd(dev, GC9307_SWRESET);
    if (ret) return ret;
    mdelay(120);
    
    /* Sleep out */
    ret = gc9307_spi_write_cmd(dev, GC9307_SLPOUT);
    if (ret) return ret;
    mdelay(120);
    
    /* Initial commands */
    ret = gc9307_spi_write_cmd(dev, 0xfe);
    if (ret) return ret;
    ret = gc9307_spi_write_cmd(dev, 0xef);
    if (ret) return ret;
    
    /* Memory access control */
    ret = gc9307_spi_write_cmd(dev, GC9307_MADCTL);
    if (ret) return ret;
    if (priv->rotate == 0) {
        ret = gc9307_spi_write_data(dev, 0xe8);
    } else if (priv->rotate == 180) {
        ret = gc9307_spi_write_data(dev, 0x38);
    } else {
        ret = gc9307_spi_write_data(dev, 0xe8); /* default 0 degrees */
    }
    if (ret) return ret;
    
    /* Interface pixel format - 16-bit color */
    ret = gc9307_spi_write_cmd(dev, GC9307_COLMOD);
    if (ret) return ret;
    ret = gc9307_spi_write_data(dev, 0x05);
    if (ret) return ret;
    
    return 0;
}

static int gc9307_init_gamma_settings(struct udevice *dev)
{
    int ret;

    /* Power control settings */
    ret = gc9307_spi_write_cmd(dev, 0x86);
    if (ret) return ret;
    ret = gc9307_spi_write_data(dev, 0x98);
    if (ret) return ret;

    ret = gc9307_spi_write_cmd(dev, 0x89);
    if (ret) return ret;
    ret = gc9307_spi_write_data(dev, 0x03);
    if (ret) return ret;

    ret = gc9307_spi_write_cmd(dev, 0x8b);
    if (ret) return ret;
    ret = gc9307_spi_write_data(dev, 0x80);
    if (ret) return ret;

    ret = gc9307_spi_write_cmd(dev, 0x8d);
    if (ret) return ret;
    ret = gc9307_spi_write_data(dev, 0x22);
    if (ret) return ret;

    ret = gc9307_spi_write_cmd(dev, 0x8e);
    if (ret) return ret;
    ret = gc9307_spi_write_data(dev, 0x0f);
    if (ret) return ret;

    /* Frame rate control */
    ret = gc9307_spi_write_cmd(dev, 0xe8);
    if (ret) return ret;
    ret = gc9307_spi_write_data(dev, 0x12);
    if (ret) return ret;
    ret = gc9307_spi_write_data(dev, 0x00);
    if (ret) return ret;

    /* Power control settings */
    ret = gc9307_spi_write_cmd(dev, 0xc3);
    if (ret) return ret;
    ret = gc9307_spi_write_data(dev, 0x47);
    if (ret) return ret;

    ret = gc9307_spi_write_cmd(dev, 0xc4);
    if (ret) return ret;
    ret = gc9307_spi_write_data(dev, 0x28);
    if (ret) return ret;

    ret = gc9307_spi_write_cmd(dev, 0xc9);
    if (ret) return ret;
    ret = gc9307_spi_write_data(dev, 0x00);
    if (ret) return ret;

    /* Extended command set */
    ret = gc9307_spi_write_cmd(dev, 0xff);
    if (ret) return ret;
    ret = gc9307_spi_write_data(dev, 0x62);
    if (ret) return ret;

    /* Display enhancement */
    ret = gc9307_spi_write_cmd(dev, 0x99);
    if (ret) return ret;
    ret = gc9307_spi_write_data(dev, 0x3e);
    if (ret) return ret;

    ret = gc9307_spi_write_cmd(dev, 0x9d);
    if (ret) return ret;
    ret = gc9307_spi_write_data(dev, 0x4b);
    if (ret) return ret;

    return 0;
}

static int gc9307_init_gamma_correction(struct udevice *dev)
{
    int ret;

    /* Positive Gamma Control */
    ret = gc9307_spi_write_cmd(dev, 0xF0);
    if (ret) return ret;
    ret = gc9307_spi_write_data(dev, 0x07);
    if (ret) return ret;
    ret = gc9307_spi_write_data(dev, 0x0b);
    if (ret) return ret;
    ret = gc9307_spi_write_data(dev, 0x0c);
    if (ret) return ret;
    ret = gc9307_spi_write_data(dev, 0x0a);
    if (ret) return ret;
    ret = gc9307_spi_write_data(dev, 0x06);
    if (ret) return ret;
    ret = gc9307_spi_write_data(dev, 0x31);
    if (ret) return ret;

    /* Negative Gamma Control */
    ret = gc9307_spi_write_cmd(dev, 0xF2);
    if (ret) return ret;
    ret = gc9307_spi_write_data(dev, 0x07);
    if (ret) return ret;
    ret = gc9307_spi_write_data(dev, 0x07);
    if (ret) return ret;
    ret = gc9307_spi_write_data(dev, 0x04);
    if (ret) return ret;
    ret = gc9307_spi_write_data(dev, 0x06);
    if (ret) return ret;
    ret = gc9307_spi_write_data(dev, 0x06);
    if (ret) return ret;
    ret = gc9307_spi_write_data(dev, 0x21);
    if (ret) return ret;

    /* Positive Gamma Correction */
    ret = gc9307_spi_write_cmd(dev, 0xF1);
    if (ret) return ret;
    ret = gc9307_spi_write_data(dev, 0x4a);
    if (ret) return ret;
    ret = gc9307_spi_write_data(dev, 0x78);
    if (ret) return ret;
    ret = gc9307_spi_write_data(dev, 0x76);
    if (ret) return ret;
    ret = gc9307_spi_write_data(dev, 0x33);
    if (ret) return ret;
    ret = gc9307_spi_write_data(dev, 0x2f);
    if (ret) return ret;
    ret = gc9307_spi_write_data(dev, 0xaf);
    if (ret) return ret;

    /* Negative Gamma Correction */
    ret = gc9307_spi_write_cmd(dev, 0xF3);
    if (ret) return ret;
    ret = gc9307_spi_write_data(dev, 0x38);
    if (ret) return ret;
    ret = gc9307_spi_write_data(dev, 0x74);
    if (ret) return ret;
    ret = gc9307_spi_write_data(dev, 0x72);
    if (ret) return ret;
    ret = gc9307_spi_write_data(dev, 0x22);
    if (ret) return ret;
    ret = gc9307_spi_write_data(dev, 0x28);
    if (ret) return ret;
    ret = gc9307_spi_write_data(dev, 0x6f);
    if (ret) return ret;

    /* Tearing Effect Line Off */
    ret = gc9307_spi_write_cmd(dev, 0x34);
    if (ret) return ret;

    return 0;
}

static int gc9307_video_sync(struct udevice *vid)
{
    struct video_priv *uc_priv = dev_get_uclass_priv(vid);
    struct gc9307_priv *priv = dev_get_priv(vid);
    u8 *fb = uc_priv->fb;
    int ret;

    if (!fb)
        return -EINVAL;

    ret = dm_spi_claim_bus(priv->spi_dev);
    if (ret) {
        printf(vid, "Failed to claim SPI bus: %d\n", ret);
        return ret;
    }

    /* Set address window to full screen */
    ret = gc9307_set_address_window(vid, 0, 0, priv->width - 1, priv->height - 1);
    if (ret)
        goto release_bus;

    /* Start memory write */
    ret = gc9307_spi_write_cmd(vid, GC9307_RAMWR);
    if (ret)
        goto release_bus;

    /* Send framebuffer data */
    ret = gc9307_spi_write_data_buf(vid, fb, uc_priv->fb_size);

release_bus:
    dm_spi_release_bus(priv->spi_dev);
    return ret;
}

static int gc9307_probe(struct udevice *dev)
{
    struct video_priv *uc_priv = dev_get_uclass_priv(dev);
    struct gc9307_priv *priv = dev_get_priv(dev);
    int ret;

    /* Get SPI device */
    priv->spi_dev = dev;

    /* Get GPIO descriptors */
    ret = gpio_request_by_name(dev, "reset-gpios", 0, &priv->reset_gpio, GPIOD_IS_OUT);
    if (ret) {
        dev_err(dev, "Failed to get reset GPIO: %d\n", ret);
        return ret;
    }

    ret = gpio_request_by_name(dev, "dc-gpios", 0, &priv->dc_gpio, GPIOD_IS_OUT);
    if (ret) {
        dev_err(dev, "Failed to get DC GPIO: %d\n", ret);
        return ret;
    }

    ret = gpio_request_by_name(dev, "cs-gpios", 0, &priv->cs_gpio, GPIOD_IS_OUT);
    if (ret) {
        dev_err(dev, "Failed to get CS GPIO: %d\n", ret);
        return ret;
    }

    ret = gpio_request_by_name(dev, "led-gpios", 0, &priv->led_gpio, GPIOD_IS_OUT);
    if (ret) {
        dev_err(dev, "Failed to get LED GPIO: %d\n", ret);
        return ret;
    }

    /* Get display properties from device tree */
    priv->width = dev_read_u32_default(dev, "width", LCD_WIDTH);
    priv->height = dev_read_u32_default(dev, "height", LCD_HEIGHT);
    priv->rotate = dev_read_u32_default(dev, "rotate", 0);

    /* Set up video properties */
    uc_priv->bpix = VIDEO_BPP16;
    uc_priv->xsize = priv->width;
    uc_priv->ysize = priv->height;
    uc_priv->rot = 0;

    /* Enable backlight */
    ret = dm_gpio_set_value(&priv->led_gpio, 1);
    if (ret) {
        printf(dev, "Failed to enable backlight: %d\n", ret);
        return ret;
    }

    /* Initialize display */
    ret = gc9307_init_display(dev);
    if (ret) {
        printf(dev, "Failed to initialize display: %d\n", ret);
        return ret;
    }

    ret = gc9307_init_gamma_settings(dev);
    if (ret) {
        printf(dev, "Failed to initialize gamma settings: %d\n", ret);
        return ret;
    }

    ret = gc9307_init_gamma_correction(dev);
    if (ret) {
        printf(dev, "Failed to initialize gamma correction: %d\n", ret);
        return ret;
    }

    /* Clear screen */
    ret = gc9307_clear_screen(dev);
    if (ret) {
        printf(dev, "Failed to clear screen: %d\n", ret);
        return ret;
    }

    /* Display on */
    ret = gc9307_spi_write_cmd(dev, GC9307_DISPON);
    if (ret) {
        printf(dev, "Failed to turn on display: %d\n", ret);
        return ret;
    }

    printf(dev, "GC9307 display initialized (%dx%d)\n", priv->width, priv->height);

    return 0;
}

static int gc9307_bind(struct udevice *dev)
{
    struct video_uc_plat *plat = dev_get_uclass_plat(dev);
    u32 width, height;

    /* Get display size from device tree */
    width = dev_read_u32_default(dev, "width", LCD_WIDTH);
    height = dev_read_u32_default(dev, "height", LCD_HEIGHT);

    /* Set framebuffer size (16-bit color) */
    plat->size = width * height * 2;

    return 0;
}

static const struct video_ops gc9307_ops = {
    .video_sync = gc9307_video_sync,
};

static const struct udevice_id gc9307_ids[] = {
    { .compatible = "victel,gc9307-uboot" },
    { }
};

U_BOOT_DRIVER(gc9307_video) = {
    .name = "gc9307_video",
    .id = UCLASS_VIDEO,
    .of_match = gc9307_ids,
    .ops = &gc9307_ops,
    .bind = gc9307_bind,
    .probe = gc9307_probe,
    .priv_auto = sizeof(struct gc9307_priv),
    .flags = DM_FLAG_PRE_RELOC,
};
